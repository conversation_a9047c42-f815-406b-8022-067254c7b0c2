# 🌤️ Weather Tool Guide

## Giớ<PERSON> thiệu

Weather Tool là một công cụ MCP (Model Context Protocol) cho phép bạn lấy thông tin thời tiết hiện tại của bất kỳ thành phố nào trên thế giới. Tool này sử dụng OpenWeatherMap API để cung cấp dữ liệu thời tiết chính xác và chi tiết.

## Tính năng chính

✅ **Thông tin thời tiết đầy đủ**: <PERSON><PERSON><PERSON><PERSON> đ<PERSON>, đ<PERSON> <PERSON><PERSON>, <PERSON><PERSON> suất, tốc độ gió  
✅ **Hỗ trợ đa đơn vị**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  
✅ **Thông tin mặt trời**: Thời gian mặt trời mọc/lặn  
✅ **Tọa độ địa lý**: <PERSON><PERSON> độ và kinh độ chính xác  
✅ **Múi giờ**: Hiển thị múi giờ địa phương  
✅ **Mã quốc gia**: Hỗ trợ mã ISO 3166 để định vị chính xác hơn  

## Thiết lập

### 1. Lấy API Key từ OpenWeatherMap

1. Truy cập [OpenWeatherMap.org](https://openweathermap.org/api)
2. Đăng ký tài khoản miễn phí
3. Tạo API key mới
4. Sao chép API key để sử dụng

### 2. Cấu hình Environment Variable

Thêm API key vào environment variables:

```bash
# Trong file .env hoặc export trực tiếp
export OPENWEATHER_API_KEY="your_api_key_here"

# Hoặc thêm vào file .env
echo "OPENWEATHER_API_KEY=your_api_key_here" >> .env
```

### 3. Khởi động MCP Server

```bash
# STDIO transport (khuyến nghị)
npm run start:server

# HTTP transport
npm run start:server:http
```

## Cách sử dụng

### Cú pháp cơ bản

```json
{
  "city": "Tên thành phố",
  "country": "Mã quốc gia (tùy chọn)",
  "units": "metric|imperial|kelvin"
}
```

### Các ví dụ sử dụng

#### 1. Thành phố Việt Nam

```json
{
  "city": "Ho Chi Minh City",
  "country": "VN",
  "units": "metric"
}
```

```json
{
  "city": "Ha Noi",
  "country": "VN",
  "units": "metric"
}
```

```json
{
  "city": "Da Nang",
  "country": "VN",
  "units": "metric"
}
```

#### 2. Thành phố quốc tế

```json
{
  "city": "New York",
  "country": "US",
  "units": "imperial"
}
```

```json
{
  "city": "Tokyo",
  "country": "JP",
  "units": "metric"
}
```

```json
{
  "city": "London",
  "country": "GB",
  "units": "metric"
}
```

#### 3. Không cần mã quốc gia

```json
{
  "city": "Paris",
  "units": "metric"
}
```

### Đơn vị nhiệt độ

- **metric**: Celsius (°C) - Khuyến nghị cho Việt Nam
- **imperial**: Fahrenheit (°F) - Phổ biến ở Mỹ
- **kelvin**: Kelvin (K) - Đơn vị khoa học

## Response format

Tool sẽ trả về dữ liệu đầy đủ bao gồm:

```json
{
  "summary": "Current weather in Ho Chi Minh City, VN",
  "weather": {
    "condition": "broken clouds",
    "temperature": "28.5°C",
    "feelsLike": "32.1°C",
    "humidity": "74%",
    "windSpeed": "3.2 m/s",
    "pressure": "1013 hPa",
    "cloudiness": "75%"
  },
  "location": {
    "coordinates": "10.8231°N, 106.6297°E",
    "timezone": "UTC+7"
  },
  "sun": {
    "sunrise": "2024-01-15T05:45:00.000Z",
    "sunset": "2024-01-15T17:30:00.000Z"
  },
  "details": {
    // Dữ liệu chi tiết đầy đủ
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Sử dụng với Agent

Bạn có thể sử dụng weather tool với agent:

```bash
npm run start:agent "Hãy cho tôi biết thời tiết hiện tại ở Hồ Chí Minh"
```

```bash
npm run start:agent "What's the weather like in Tokyo today?"
```

```bash
npm run start:agent "Compare weather between Hanoi and Ho Chi Minh City"
```

## Test với HTTP API

Nếu sử dụng HTTP transport:

```bash
# 1. Khởi tạo session
curl -X POST http://localhost:3010/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2025-03-26",
      "capabilities": {},
      "clientInfo": {"name": "test-client", "version": "1.0.0"}
    }
  }'

# 2. Gọi weather tool
curl -X POST http://localhost:3010/mcp \
  -H "Content-Type: application/json" \
  -H "Accept: application/json, text/event-stream" \
  -H "X-Session-ID: session-123" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/call",
    "params": {
      "name": "get_weather",
      "arguments": {
        "city": "Ho Chi Minh City",
        "country": "VN",
        "units": "metric"
      }
    }
  }'
```

## Xử lý lỗi phổ biến

### 1. API Key không hợp lệ
```
Error: Invalid API key. Please check your OPENWEATHER_API_KEY environment variable.
```
**Giải pháp**: Kiểm tra lại API key và đảm bảo đã set đúng environment variable.

### 2. Thành phố không tìm thấy
```
Error: City "XYZ" not found. Please check the city name and country code.
```
**Giải pháp**: 
- Kiểm tra chính tả tên thành phố
- Thêm mã quốc gia để định vị chính xác hơn
- Thử tên tiếng Anh của thành phố

### 3. Vượt quá giới hạn API
```
Error: API rate limit exceeded. Please try again later.
```
**Giải pháp**: Đợi một chút và thử lại. Free plan có giới hạn 60 calls/minute.

## Mẹo sử dụng

1. **Sử dụng mã quốc gia**: Luôn thêm mã quốc gia để tránh nhầm lẫn
2. **Tên tiếng Anh**: Sử dụng tên tiếng Anh cho kết quả tốt nhất
3. **Cache kết quả**: Tránh gọi quá nhiều cho cùng một thành phố
4. **Đơn vị phù hợp**: Chọn đơn vị nhiệt độ phù hợp với người dùng

## Troubleshooting

### Build errors
```bash
npm run rebuild
```

### Server không khởi động
```bash
# Kiểm tra logs
npm run start:server 2>&1 | grep -i error

# Kiểm tra environment variables
echo $OPENWEATHER_API_KEY
```

### Tool không hiển thị
Đảm bảo:
1. Build thành công
2. API key đã được set
3. Server đã khởi động hoàn tất
4. Client đã kết nối đúng

## Giới hạn và Lưu ý

- **Free plan**: 60 calls/minute, 1,000 calls/day
- **Dữ liệu**: Cập nhật mỗi 10 phút
- **Độ chính xác**: Dựa trên dữ liệu từ các trạm khí tượng
- **Múi giờ**: Tự động tính theo vị trí địa lý

## Liên kết hữu ích

- [OpenWeatherMap API Documentation](https://openweathermap.org/api)
- [ISO 3166 Country Codes](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
- [MCP Specification](https://modelcontextprotocol.io/)

---

**Chúc bạn sử dụng Weather Tool hiệu quả! 🌤️** 