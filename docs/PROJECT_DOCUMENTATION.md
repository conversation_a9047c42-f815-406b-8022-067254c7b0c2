# 📚 Tài liệu Project MCP TypeScript Template

## 🎯 Tổng quan Project

**MCP TypeScript Template** là một template production-ready được thiết kế để phát triển các ứng dụng **Model Context Protocol (MCP)**. Project này cung cấp một framework hoàn chỉnh để xây dựng MCP servers, clients và autonomous agents với khả năng tích hợp đa dạng.

### Mục tiêu chính
- Cung cấp template chuẩn cho việc phát triển MCP applications
- Hỗ trợ xây dựng autonomous agents có thể kết nối với nhiều MCP servers
- Tích hợp sẵn các utilities production-ready (logging, error handling, security)
- Dễ dàng mở rộng và tùy chỉnh cho các use cases cụ thể

## 🏗️ Kiến trúc và Thành phần chính

Project được tổ chức theo kiến trúc 3 phần chính:

### 1. 🤖 Agent Framework (`src/agent/`)
**<PERSON><PERSON>c đích**: Autonomous agents có thể thực hiện các task phức tạp bằng cách sử dụng tools từ nhiều MCP servers.

**Thành phần chính**:
- `agent-core/agent.ts`: Core logic của agent, quản lý lifecycle và tương tác với MCP servers
- `cli/`: Command-line interface để chạy agent từ terminal
- `cli/boot.ts`: Bootstrap script khởi tạo các services cần thiết

**Tính năng**:
- Kết nối đồng thời với nhiều MCP servers
- Tự động khám phá và sử dụng tools có sẵn
- Xử lý task phức tạp thông qua LLM integration (OpenRouter)
- Streaming response support

### 2. 🔌 MCP Server (`src/mcp-server/`)
**Mục đích**: Host các custom tools và resources, làm chúng có sẵn cho clients và agents.

**Cấu trúc**:
```
mcp-server/
├── server.ts              # Main server orchestration
├── tools/                 # Custom MCP tools
│   ├── echoTool/          # Echo message tool
│   ├── weatherTool/       # Weather information tool
│   ├── catFactFetcher/    # Random cat facts tool
│   └── imageTest/         # Image fetching test tool
├── resources/             # MCP resources
│   └── echoResource/      # Echo resource example
└── transports/            # Communication protocols
    ├── stdioTransport.js  # STDIO transport
    ├── httpTransport.js   # HTTP transport
    └── auth/              # Authentication middleware
```

**Built-in Tools**:
- `echo_message`: Lặp lại message với formatting options
- `get_weather`: Lấy thông tin thời tiết từ OpenWeatherMap API
- `get_random_cat_fact`: Lấy random facts về mèo
- `fetch_image_test`: Test tool để fetch images

**Transport Support**:
- **STDIO**: Process-based communication (default)
- **HTTP**: RESTful API với Server-Sent Events
- **Authentication**: JWT tokens hoặc OAuth 2.1

### 3. 💻 MCP Client (`src/mcp-client/`)
**Mục đích**: Kết nối và tương tác với các MCP servers khác.

**Thành phần**:
- `core/`: Client connection và management logic
- `client-config/`: Configuration loading và validation
- `transports/`: Client transport implementations

**Tính năng**:
- Multi-server connection management
- Configuration-driven setup qua JSON files
- Transport-agnostic (STDIO, HTTP)
- Automatic reconnection và error handling

### 4. 🛠️ Supporting Components

#### Configuration (`src/config/`)
- Environment-based configuration với Zod validation
- Hierarchical config: package.json → Environment → Defaults
- Type-safe configuration management

#### Services (`src/services/`)
- **LLM Providers**: OpenRouter integration cho AI capabilities
- **DuckDB**: In-process analytical database
- **Supabase**: Backend-as-a-Service integration

#### Utils (`src/utils/`)
- **Logging**: Winston-based structured logging
- **Error Handling**: Centralized error management với typed errors
- **Security**: Input sanitization, rate limiting
- **Parsing**: JSON parsing utilities
- **Network**: Network-related utilities

#### Types (`src/types-global/`)
- Global TypeScript type definitions
- Error codes và error types
- Shared interfaces

## 🚀 Cách triển khai Tool mới cho MCP Server

### Bước 1: Tạo cấu trúc thư mục

```bash
mkdir -p src/mcp-server/tools/yourToolName
cd src/mcp-server/tools/yourToolName
```

### Bước 2: Tạo các file cần thiết

#### 2.1. `logic.ts` - Core business logic

```typescript
/**
 * @fileoverview Core logic cho yourToolName tool
 */

import { z } from "zod";
import { logger, type RequestContext } from "../../../utils/index.js";

// Định nghĩa Zod schema cho input validation
export const YourToolInputSchema = z.object({
  // Định nghĩa các parameters cần thiết
  param1: z.string().min(1).describe("Mô tả parameter 1"),
  param2: z.number().optional().default(10).describe("Mô tả parameter 2"),
});

// TypeScript type từ schema
export type YourToolInput = z.infer<typeof YourToolInputSchema>;

// Interface cho response
export interface YourToolResponse {
  result: string;
  timestamp: string;
  // Các fields khác...
}

/**
 * Core logic function
 */
export async function yourToolLogic(
  params: YourToolInput,
  context: RequestContext,
): Promise<YourToolResponse> {
  logger.debug("Processing your tool logic", {
    ...context,
    toolInput: params,
  });

  // Implement business logic ở đây
  const result = `Processed: ${params.param1}`;

  const response: YourToolResponse = {
    result,
    timestamp: new Date().toISOString(),
  };

  logger.debug("Tool processed successfully", {
    ...context,
    responseSummary: { resultLength: result.length },
  });

  return response;
}
```

#### 2.2. `registration.ts` - MCP server registration

```typescript
/**
 * @fileoverview Registration logic cho yourToolName tool
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import type { CallToolResult } from "@modelcontextprotocol/sdk/types.js";
import { BaseErrorCode, McpError } from "../../../types-global/errors.js";
import {
  ErrorHandler,
  logger,
  RequestContext,
  requestContextService,
} from "../../../utils/index.js";
import {
  YourToolInput,
  YourToolInputSchema,
  yourToolLogic,
} from "./logic.js";

export const registerYourTool = async (server: McpServer): Promise<void> => {
  const toolName = "your_tool_name";
  const toolDescription = `
Mô tả chi tiết về tool của bạn.

Features:
- Feature 1
- Feature 2

Examples:
- {"param1": "example", "param2": 5}
`;

  const registrationContext: RequestContext =
    requestContextService.createRequestContext({
      operation: "RegisterTool",
      toolName: toolName,
    });

  logger.info(`Registering tool: '${toolName}'`, registrationContext);

  await ErrorHandler.tryCatch(
    async () => {
      server.tool(
        toolName,
        toolDescription,
        YourToolInputSchema.shape,
        async (
          params: YourToolInput,
          mcpContext: unknown,
        ): Promise<CallToolResult> => {
          const handlerContext: RequestContext =
            requestContextService.createRequestContext({
              parentRequestId: registrationContext.requestId,
              operation: "HandleToolRequest",
              toolName: toolName,
              mcpToolContext: mcpContext,
              input: params,
            });

          try {
            const result = await yourToolLogic(params, handlerContext);
            
            return {
              content: [
                { 
                  type: "text", 
                  text: JSON.stringify(result, null, 2) 
                },
              ],
              isError: false,
            };
          } catch (error) {
            const handledError = ErrorHandler.handleError(error, {
              operation: "yourToolHandler",
              context: handlerContext,
              input: params,
            });

            const mcpError =
              handledError instanceof McpError
                ? handledError
                : new McpError(
                    BaseErrorCode.INTERNAL_ERROR,
                    "An unexpected error occurred in your tool.",
                    { originalErrorName: handledError.name },
                  );

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify({
                    error: {
                      code: mcpError.code,
                      message: mcpError.message,
                      details: mcpError.details,
                    },
                  }),
                },
              ],
              isError: true,
            };
          }
        },
      );

      logger.info(
        `Tool '${toolName}' registered successfully.`,
        registrationContext,
      );
    },
    {
      operation: `RegisteringTool_${toolName}`,
      context: registrationContext,
      errorCode: BaseErrorCode.INITIALIZATION_FAILED,
      critical: true,
    },
  );
};
```

#### 2.3. `index.ts` - Barrel export

```typescript
/**
 * @fileoverview Barrel file cho yourToolName tool
 */

export { registerYourTool } from "./registration.js";
```

### Bước 3: Đăng ký tool với server

Thêm tool vào `src/mcp-server/server.ts`:

```typescript
// Import tool registration
import { registerYourTool } from "./tools/yourToolName/index.js";

// Trong function createMcpServerInstance(), thêm:
await registerYourTool(server);
```

### Bước 4: Build và test

```bash
# Build project
npm run build

# Test với MCP Inspector
npm run inspector

# Hoặc test với agent
npm run start:agent "Use your_tool_name with param1='test'"
```

## 🔧 Cách cấu hình MCP với các AI Tools

### 1. Cấu hình với Cursor

#### Bước 1: Build project
```bash
npm run build
```

#### Bước 2: Tạo file cấu hình cho Cursor

Tạo file `~/.cursor/mcp_servers.json`:

```json
{
  "mcpServers": {
    "albatross-mcp": {
      "command": "node",
      "args": ["/path/to/your/project/dist/index.js"],
      "env": {
        "MCP_LOG_LEVEL": "debug",
        "MCP_TRANSPORT_TYPE": "stdio",
        "NODE_ENV": "development",
        "OPENWEATHER_API_KEY": "your_openweather_api_key",
        "OPENROUTER_API_KEY": "your_openrouter_api_key"
      }
    }
  }
}
```

#### Bước 3: Restart Cursor
Restart Cursor để load cấu hình mới.

### 2. Cấu hình với Claude Desktop

#### Bước 1: Tìm file cấu hình Claude

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

#### Bước 2: Thêm cấu hình MCP server

```json
{
  "mcpServers": {
    "albatross-mcp": {
      "command": "node",
      "args": ["/absolute/path/to/your/project/dist/index.js"],
      "env": {
        "MCP_LOG_LEVEL": "info",
        "MCP_TRANSPORT_TYPE": "stdio",
        "OPENWEATHER_API_KEY": "your_api_key_here"
      }
    }
  }
}
```

#### Bước 3: Restart Claude Desktop

### 3. Cấu hình với ChatGPT (qua MCP Client)

ChatGPT hiện tại chưa hỗ trợ trực tiếp MCP, nhưng bạn có thể sử dụng agent framework của project:

```bash
# Cấu hình agent để kết nối với MCP servers
# Edit src/mcp-client/client-config/mcp-config.json

{
  "mcpServers": {
    "local-server": {
      "command": "node",
      "args": ["dist/index.js"],
      "env": {
        "MCP_LOG_LEVEL": "debug",
        "OPENWEATHER_API_KEY": "your_api_key"
      },
      "transportType": "stdio"
    }
  }
}

# Chạy agent
npm run start:agent "Your task here"
```

### 4. Cấu hình Environment Variables

Tạo file `.env` trong root directory:

```bash
# MCP Server Configuration
MCP_SERVER_NAME=albatross-mcp
MCP_SERVER_VERSION=1.0.0
MCP_LOG_LEVEL=debug
MCP_TRANSPORT_TYPE=stdio

# API Keys
OPENWEATHER_API_KEY=your_openweather_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here

# HTTP Transport (nếu sử dụng)
MCP_HTTP_PORT=3010
MCP_HTTP_HOST=127.0.0.1
MCP_AUTH_SECRET_KEY=your_32_character_secret_key_here

# Database (nếu sử dụng)
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🧪 Testing và Debugging

### 1. Sử dụng MCP Inspector

```bash
# Chạy MCP Inspector để test tools
npm run inspector
```

### 2. Test với Agent CLI

```bash
# Test specific tool
npm run start:agent "Use echo_message to say hello world"

# Test weather tool
npm run start:agent "Get weather for Ho Chi Minh City"
```

### 3. Debug Logging

Logs được lưu trong thư mục `logs/`:
- `combined.log`: Tất cả logs
- `error.log`: Chỉ errors
- `debug.log`: Debug information
- `interactions.log`: LLM interactions

### 4. HTTP Transport Testing

```bash
# Start HTTP server
MCP_TRANSPORT_TYPE=http npm run start:http

# Test với curl
curl -X POST http://localhost:3010/mcp \
  -H "Content-Type: application/json" \
  -d '{"method": "tools/list"}'
```

## 📝 Best Practices

### 1. Tool Development
- Luôn sử dụng Zod schema cho input validation
- Implement comprehensive error handling
- Thêm detailed logging cho debugging
- Viết clear documentation và examples

### 2. Configuration
- Sử dụng environment variables cho sensitive data
- Validate configuration với Zod schemas
- Provide sensible defaults
- Document all configuration options

### 3. Security
- Sanitize tất cả user inputs
- Implement rate limiting cho HTTP transport
- Sử dụng proper authentication cho production
- Never log sensitive information

### 4. Performance
- Implement connection pooling cho database connections
- Use streaming cho large responses
- Cache frequently accessed data
- Monitor resource usage

## 🚀 Deployment

### 1. Production Build

```bash
npm run build
```

### 2. Environment Setup

```bash
# Set production environment
export NODE_ENV=production
export MCP_LOG_LEVEL=info

# Set required API keys
export OPENWEATHER_API_KEY=your_production_key
```

### 3. Process Management

Sử dụng PM2 cho production deployment:

```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start dist/index.js --name "albatross-mcp"

# Monitor
pm2 monit

# Logs
pm2 logs albatross-mcp
```

### 4. Docker Deployment

```dockerfile
FROM node:20-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY dist/ ./dist/
COPY .env ./

EXPOSE 3010
CMD ["node", "dist/index.js"]
```

## 🔍 Troubleshooting

### Common Issues

1. **Tool không được recognize**
   - Kiểm tra tool đã được registered trong `server.ts`
   - Verify build process đã chạy thành công
   - Check logs cho registration errors

2. **API Key errors**
   - Verify environment variables được set correctly
   - Check API key validity và permissions
   - Ensure .env file được load properly

3. **Connection issues**
   - Check transport type configuration
   - Verify paths trong MCP client config
   - Check firewall settings cho HTTP transport

4. **Performance issues**
   - Monitor logs cho slow operations
   - Check database connection pooling
   - Implement caching cho expensive operations

Để biết thêm chi tiết, tham khảo logs trong thư mục `logs/` và documentation trong thư mục `docs/`.