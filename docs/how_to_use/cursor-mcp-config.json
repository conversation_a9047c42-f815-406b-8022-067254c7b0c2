{"mcpServers": {"albatross-mcp": {"command": "node", "args": ["/Users/<USER>/workspace/code/albatross-foundation/albatross-mcp/dist/index.js"], "env": {"MCP_LOG_LEVEL": "debug", "MCP_TRANSPORT_TYPE": "stdio", "NODE_ENV": "development", "KIOTVIET_CLIENT_ID": "KIOTVIET_CLIENT_ID", "KIOTVIET_CLIENT_SECRET": "KIOTVIET_CLIENT_SECRET", "KIOTVIET_RETAILER": "KIOTVIET_RETAILER", "KIOTVIET_BASE_URL": "https://public.kiotapi.com", "KIOTVIET_TOKEN_URL": "https://id.kiotviet.vn/connect/token"}}}}