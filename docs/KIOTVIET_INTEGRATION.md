# KiotViet API Integration Guide

This document provides a comprehensive guide for integrating with the KiotViet API using the MCP TypeScript Template.

## Overview

The KiotViet integration provides:
- **Token Management**: Automatic OAuth 2.0 token acquisition and refresh
- **API Service Layer**: Centralized service for KiotViet API calls
- **MCP Tool**: Ready-to-use tool for accessing KiotViet product categories
- **Type Safety**: Full TypeScript support with Zod validation

## Architecture

```
src/services/kiotviet/          # KiotViet API Service Layer
├── kiotVietService.ts          # Main service implementation
├── types.ts                    # TypeScript type definitions
├── config.ts                   # Configuration and validation
└── index.ts                    # Public exports

src/mcp-server/tools/kiotviet-categories/  # MCP Tool Implementation
├── logic.ts                    # Core business logic
├── registerTool.ts             # MCP server registration
└── index.ts                    # Public exports
```

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```bash
# KiotViet API Configuration
KIOTVIET_CLIENT_ID=your_client_id_here
KIOTVIET_CLIENT_SECRET=your_client_secret_here
KIOTVIET_RETAILER=your_retailer_code_here

# Optional: Custom endpoints (defaults provided)
KIOTVIET_BASE_URL=https://public.kiotapi.com
KIOTVIET_TOKEN_URL=https://id.kiotviet.vn/connect/token
```

### Getting KiotViet Credentials

1. **Register Your Application**:
   - Visit the KiotViet Developer Portal
   - Create a new application
   - Note down your `Client ID` and `Client Secret`

2. **Get Retailer Code**:
   - Your retailer code is your store identifier in KiotViet
   - This is typically provided when you set up your KiotViet account

## Usage

### Using the MCP Tool

The `kiotviet_categories` tool supports three actions:

#### 1. List All Categories

```json
{
  "action": "list"
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "categoryId": 1,
      "categoryName": "Electronics",
      "categoryCode": "ELEC",
      "parentId": null,
      "hasChild": true,
      "createdDate": "2024-01-01T00:00:00Z",
      "modifiedDate": "2024-01-01T00:00:00Z"
    }
  ],
  "count": 1,
  "message": "Retrieved 1 product categories from KiotViet"
}
```

#### 2. Get Category by ID

```json
{
  "action": "get",
  "categoryId": 1
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "categoryId": 1,
    "categoryName": "Electronics",
    "categoryCode": "ELEC",
    "parentId": null,
    "hasChild": true,
    "createdDate": "2024-01-01T00:00:00Z",
    "modifiedDate": "2024-01-01T00:00:00Z"
  },
  "message": "Retrieved category: Electronics"
}
```

#### 3. Test API Connection

```json
{
  "action": "test"
}
```

**Response:**
```json
{
  "success": true,
  "message": "KiotViet API connection successful"
}
```

### Using the Service Layer Directly

```typescript
import { KiotVietService, createKiotVietConfig } from '../services/kiotviet/index.js';

// Initialize service
const config = createKiotVietConfig();
const kiotVietService = new KiotVietService(config);

// Get all categories
const categories = await kiotVietService.getProductCategories();

// Get specific category
const category = await kiotVietService.getCategoryById(1);

// Test connection
const isConnected = await kiotVietService.testConnection();
```

## Creating Additional Tools

To create more KiotViet tools, follow this pattern:

### 1. Add New API Methods to Service

```typescript
// In src/services/kiotviet/kiotVietService.ts
async getProducts(): Promise<KiotVietProduct[]> {
  return this.makeApiRequest<KiotVietApiResponse<KiotVietProduct[]>>('/products');
}
```

### 2. Create New Tool

```typescript
// src/mcp-server/tools/kiotviet-products/logic.ts
export const KiotVietProductsInputSchema = z.object({
  action: z.enum(['list', 'get']),
  productId: z.number().optional(),
});

export async function kiotVietProductsLogic(
  input: KiotVietProductsInput,
  context: RequestContext
): Promise<any> {
  const service = getKiotVietService();
  
  switch (input.action) {
    case 'list':
      return await service.getProducts();
    case 'get':
      return await service.getProductById(input.productId!);
  }
}
```

### 3. Register Tool

```typescript
// src/mcp-server/tools/kiotviet-products/registerTool.ts
export const registerKiotVietProductsTool = async (server: McpServer): Promise<void> => {
  // Follow the same pattern as kiotviet-categories/registerTool.ts
};
```

## Error Handling

The integration includes comprehensive error handling:

- **Token Refresh**: Automatic token refresh with 5-minute buffer
- **API Errors**: Detailed error messages from KiotViet API
- **Network Issues**: Proper error propagation and logging
- **Validation**: Input validation using Zod schemas

## Security Considerations

- **Credentials**: Store credentials in environment variables, never in code
- **Token Storage**: Tokens are stored in memory only, not persisted
- **Rate Limiting**: Implement rate limiting for production use
- **HTTPS**: Always use HTTPS endpoints for API calls

## Testing

### Manual Testing

1. **Set up environment variables**
2. **Start the MCP server**:
   ```bash
   npm run start:stdio
   ```
3. **Use MCP Inspector**:
   ```bash
   npm run inspector
   ```
4. **Test the tool** with different actions

### Automated Testing

```typescript
// Example test
describe('KiotViet Service', () => {
  it('should fetch categories', async () => {
    const service = new KiotVietService(testConfig);
    const categories = await service.getProductCategories();
    expect(categories).toBeInstanceOf(Array);
  });
});
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Verify `CLIENT_ID`, `CLIENT_SECRET`, and `RETAILER` are correct
   - Check if credentials have proper permissions

2. **Network Errors**:
   - Verify internet connectivity
   - Check if KiotViet API endpoints are accessible

3. **Token Expiry**:
   - The service automatically handles token refresh
   - Check logs for token refresh attempts

### Debug Logging

Enable debug logging to troubleshoot issues:

```bash
MCP_LOG_LEVEL=debug npm run start:stdio
```

## API Reference

### KiotViet Service Methods

- `getProductCategories()`: Fetch all product categories
- `getCategoryById(id)`: Fetch specific category by ID
- `testConnection()`: Test API connectivity

### Configuration Options

- `clientId`: KiotViet application client ID
- `clientSecret`: KiotViet application client secret
- `retailer`: Store identifier
- `baseUrl`: API base URL (default: https://public.kiotapi.com)
- `tokenUrl`: Token endpoint URL (default: https://id.kiotviet.vn/connect/token)

## Next Steps

1. **Extend API Coverage**: Add more KiotViet API endpoints
2. **Add Caching**: Implement response caching for better performance
3. **Add Webhooks**: Support KiotViet webhook notifications
4. **Add Batch Operations**: Support bulk operations for efficiency

## Support

For issues related to:
- **KiotViet API**: Contact KiotViet support
- **Integration Code**: Create an issue in this repository
- **MCP Protocol**: Refer to MCP documentation