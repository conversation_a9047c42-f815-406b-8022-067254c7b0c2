# KiotViet HTTP Transport Troubleshooting Guide

## Issue: "Cannot write headers after they are sent to the client" (ERR_HTTP_HEADERS_SENT)

### Root Cause Analysis

The error "Cannot write headers after they are sent to the client" occurs when there are multiple attempts to send HTTP response headers. This typically happens in one of these scenarios:

1. **Node.js Version Incompatibility**: Using Node.js v14.21.3 with code that requires >=20.0.0
2. **Response Handling Race Condition**: Multiple response attempts in the MCP transport layer
3. **Error Handling Conflicts**: Both tool handler and HTTP error handler trying to send responses

### Solution Steps

#### 1. Upgrade Node.js (CRITICAL)

**Current Issue**: You're using Node.js v14.21.3, but the project requires >=20.0.0

```bash
# Check current version
node --version

# Using nvm (recommended)
nvm install 20
nvm use 20

# Or using Homebrew on macOS
brew install node@20

# Verify upgrade
node --version  # Should show v20.x.x or higher
```

#### 2. Fixed Tool Registration Pattern

The KiotViet Categories tool registration has been updated to follow the established error handling pattern used by other tools in the codebase:

**Before** (Problematic):
- Used custom error handling that might conflict with HTTP transport
- Potential for multiple response attempts

**After** (Fixed):
- Follows the standard try/catch pattern used by other tools
- Proper error handling with `ErrorHandler.handleError`
- Single response path for both success and error cases

#### 3. Environment Variables Setup

Ensure these environment variables are set:

```bash
# Required KiotViet API credentials
KIOTVIET_CLIENT_ID=your_client_id_here
KIOTVIET_CLIENT_SECRET=your_client_secret_here
KIOTVIET_RETAILER=your_retailer_code_here

# Optional (defaults provided)
KIOTVIET_BASE_URL=https://public.kiotapi.com
KIOTVIET_TOKEN_URL=https://id.kiotviet.vn/connect/token

# HTTP Transport settings
MCP_TRANSPORT_TYPE=http
MCP_HTTP_PORT=3010
MCP_HTTP_HOST=127.0.0.1

# Auth settings (for development, can be empty)
MCP_AUTH_MODE=jwt
# MCP_AUTH_SECRET_KEY=  # Can be empty in development
```

### Testing the Fix

#### Option 1: Using the Shell Script

```bash
# Make executable
chmod +x test-kiotviet-http.sh

# Run tests
./test-kiotviet-http.sh
```

#### Option 2: Using the Node.js Script

```bash
# Run tests
node test-kiotviet-http.js
```

#### Option 3: Manual curl Testing

```bash
# 1. Start the server
npm run start:http

# 2. Initialize session
curl -X POST http://localhost:3010/mcp \
  -H "Content-Type: application/json" \
  -H "Mcp-Session-Id: test-session-123" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2025-03-26",
      "capabilities": {},
      "clientInfo": {"name": "test-client", "version": "1.0.0"}
    }
  }'

# 3. Call KiotViet Categories tool
curl -X POST http://localhost:3010/mcp \
  -H "Content-Type: application/json" \
  -H "Mcp-Session-Id: test-session-123" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/call",
    "params": {
      "name": "kiotviet_categories",
      "arguments": {
        "action": "list"
      }
    }
  }'
```

### Expected Behavior After Fix

1. **Successful Response**:
   ```json
   {
     "jsonrpc": "2.0",
     "id": 2,
     "result": {
       "content": [
         {
           "type": "text",
           "text": "{\n  \"success\": true,\n  \"data\": [...],\n  \"count\": 5,\n  \"message\": \"Retrieved 5 product categories from KiotViet\"\n}"
         }
       ],
       "isError": false
     }
   }
   ```

2. **Error Response** (if KiotViet API fails):
   ```json
   {
     "jsonrpc": "2.0",
     "id": 2,
     "result": {
       "content": [
         {
           "type": "text",
           "text": "{\n  \"error\": {\n    \"code\": \"INTERNAL_ERROR\",\n    \"message\": \"...\",\n    \"details\": {...}\n  }\n}"
         }
       ],
       "isError": true
     }
   }
   ```

### Common Issues and Solutions

#### Issue: "Session not found or expired"
**Solution**: Make sure to use the same `Mcp-Session-Id` header for all requests after initialization.

#### Issue: "Authentication failed"
**Solution**: In development mode, authentication is bypassed if `MCP_AUTH_SECRET_KEY` is not set.

#### Issue: "KiotViet service configuration error"
**Solution**: Verify all KiotViet environment variables are set correctly.

#### Issue: "Port already in use"
**Solution**: The server will automatically try ports 3010-3025. Check the console output for the actual port.

### Debugging Tips

1. **Enable Debug Logging**:
   ```bash
   MCP_LOG_LEVEL=debug npm run start:http
   ```

2. **Check Server Logs**: Look for these log entries:
   - "HTTP Session created: [session-id]"
   - "KiotViet Categories tool called with action: list"
   - "HTTP transport listening at [address]"

3. **Verify Tool Registration**: The server should log:
   - "Registering resources and tools..."
   - "Resources and tools registered successfully"

### Next Steps

After upgrading Node.js and testing:

1. **Build the project**: `npm run build`
2. **Start HTTP server**: `npm run start:http`
3. **Run tests**: Use one of the provided test scripts
4. **Integrate with your application**: Use the working HTTP endpoint

The fix ensures proper response handling and eliminates the "Cannot write headers after they are sent" error by following the established error handling patterns used throughout the codebase.
