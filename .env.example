# -----------------------------------------------------------------
# MCP Server Configuration
# -----------------------------------------------------------------

# Transport type: "stdio" or "http"
MCP_TRANSPORT_TYPE=http

# Log level: "debug", "info", "warning", "error", "crit", "alert", "emerg"
MCP_LOG_LEVEL=debug

# Directory for log files
LOGS_DIR=logs/

# -----------------------------------------------------------------
# HTTP Transport Configuration (only used if MCP_TRANSPORT_TYPE=http)
# -----------------------------------------------------------------

MCP_HTTP_HOST=127.0.0.1
MCP_HTTP_PORT=3010

# Comma-separated list of allowed origins for CORS
MCP_ALLOWED_ORIGINS=http://localhost:8080

# -----------------------------------------------------------------
# Authentication Configuration
# -----------------------------------------------------------------

# Secret key for signing JWTs (must be at least 32 characters long)
# IMPORTANT: This is required for the HTTP transport and OAuth2
MCP_AUTH_SECRET_KEY=your-super-secret-key-that-is-at-least-32-characters-long

# -----------------------------------------------------------------
# Database Configuration
# -----------------------------------------------------------------

# Supabase Project URL
SUPABASE_URL=
# Supabase Anonymous Key (public, safe for client-side use)
SUPABASE_ANON_KEY=
# Supabase Service Role Key (private, for server-side admin tasks)
SUPABASE_SERVICE_ROLE_KEY=

# -----------------------------------------------------------------
# LLM Provider Configuration (Optional)
# -----------------------------------------------------------------

# API key for OpenRouter
OPENROUTER_API_KEY=

# Default LLM model to use
LLM_DEFAULT_MODEL="google/gemini-2.5-flash-preview-05-20"

# Default temperature for LLM responses
LLM_DEFAULT_TEMPERATURE=0.7

# -----------------------------------------------------------------
# KiotViet API Configuration
# -----------------------------------------------------------------

# KiotViet Client ID (from your KiotViet app registration)
KIOTVIET_CLIENT_ID=

# KiotViet Client Secret (from your KiotViet app registration)
KIOTVIET_CLIENT_SECRET=

# KiotViet Retailer code (your store identifier)
KIOTVIET_RETAILER=

# KiotViet API Base URL (default: https://public.kiotapi.com)
KIOTVIET_BASE_URL=https://public.kiotapi.com

# KiotViet Token URL (default: https://id.kiotviet.vn/connect/token)
KIOTVIET_TOKEN_URL=https://id.kiotviet.vn/connect/token
