{"name": "mcp-ts-template", "version": "1.6.3", "description": "Jumpstart Model Context Protocol (MCP) development with this production-ready TypeScript template. Build robust MCP servers and clients with built-in utilities, authentication, and service integrations. Agent framework utilizing MCP Client included.", "main": "dist/index.js", "files": ["dist"], "bin": {"mcp-ts-template": "dist/index.js"}, "exports": "./dist/index.js", "types": "dist/index.d.ts", "type": "module", "repository": {"type": "git", "url": "git+https://github.com/cyanheads/mcp-ts-template.git"}, "bugs": {"url": "https://github.com/cyanheads/mcp-ts-template/issues"}, "homepage": "https://github.com/cyanheads/mcp-ts-template#readme", "scripts": {"build": "tsc && node --loader ts-node/esm scripts/make-executable.ts dist/index.js", "start": "node dist/index.js", "start:stdio": "MCP_LOG_LEVEL=debug MCP_TRANSPORT_TYPE=stdio node dist/index.js", "start:http": "MCP_LOG_LEVEL=debug MCP_TRANSPORT_TYPE=http node dist/index.js", "start:agent": "MCP_LOG_LEVEL=debug node dist/agent/cli/boot.js", "rebuild": "node --loader ts-node/esm scripts/clean.ts && npm run build", "docs:generate": "typedoc --tsconfig ./tsconfig.typedoc.json", "depcheck": "npx depcheck", "lint": "eslint .", "lint:fix": "eslint . --fix", "tree": "node --loader ts-node/esm scripts/tree.ts", "fetch-spec": "node --loader ts-node/esm scripts/fetch-openapi-spec.ts", "format": "prettier --write \"**/*.{ts,js,json,md,html,css}\"", "inspector": "npx mcp-inspector --config mcp.json --server mcp-ts-template", "db:duckdb-example": "MCP_LOG_LEVEL=debug tsc && node dist/storage/duckdbExample.js"}, "dependencies": {"@duckdb/node-api": "^1.3.1-alpha.23", "@hono/node-server": "^1.15.0", "@modelcontextprotocol/sdk": "^1.15.0", "@supabase/supabase-js": "^2.50.3", "@types/node": "^24.0.10", "@types/sanitize-html": "^2.16.0", "@types/validator": "13.15.2", "chrono-node": "^2.8.0", "dotenv": "^16.6.1", "eslint": "^9.30.1", "hono": "^4.8.4", "ignore": "^7.0.5", "jose": "^6.0.11", "js-yaml": "^4.1.0", "node-cron": "^4.2.0", "openai": "^5.8.2", "partial-json": "^0.1.7", "sanitize-html": "^2.17.0", "tiktoken": "^1.0.21", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1", "validator": "13.15.15", "winston": "^3.17.0", "winston-transport": "^4.9.0", "zod": "^3.25.74"}, "keywords": ["typescript", "template", "mcp", "model-context-protocol", "agent", "autonomous-agent", "agent-framework", "architecture", "error-handling", "llm", "ai-integration", "mcp-server", "mcp-client", "hono", "stdio", "http", "authentication", "o<PERSON>h", "jwt", "openrouter", "duckdb", "zod"], "author": "cyanheads <<EMAIL>> (https://github.com/cyanheads/mcp-ts-template#readme)", "license": "Apache-2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/cyanheads"}, {"type": "buy_me_a_coffee", "url": "https://www.buymeacoffee.com/cyanheads"}], "engines": {"node": ">=20.0.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/js-yaml": "^4.0.9", "@types/node-cron": "^3.0.11", "axios": "^1.10.0", "depcheck": "^1.4.7", "globals": "^16.3.0", "prettier": "^3.6.2", "typedoc": "^0.28.7"}}