# Technology Stack & Build System

## Core Technologies

- **TypeScript 5.8.3** - Primary language with strict type checking
- **Node.js 20+** - Runtime environment (ESM modules)
- **Zod** - Schema validation and type safety
- **Model Context Protocol SDK** - Core MCP functionality

## Key Dependencies

### Server & Transport
- **@modelcontextprotocol/sdk** - Official MCP SDK
- **Hono** - HTTP framework for web transport
- **@hono/node-server** - Node.js adapter for Hono

### Authentication & Security
- **jose** - JWT handling
- **jsonwebtoken** - Token generation/validation
- **sanitize-html** - Input sanitization
- **validator** - Data validation utilities

### Logging & Monitoring
- **winston** - Structured logging system
- **winston-transport** - Custom transport support

### External Services
- **openai** - OpenRouter LLM integration
- **@duckdb/node-api** - In-process analytics database
- **@supabase/supabase-js** - Backend-as-a-Service
- **chrono-node** - Natural language date parsing

### Development Tools
- **ESLint** - Code linting with TypeScript rules
- **Prettier** - Code formatting
- **TypeDoc** - API documentation generation
- **ts-node** - TypeScript execution

## Build System

### Common Commands

```bash
# Development
npm run build          # Compile TypeScript to dist/
npm run rebuild        # Clean build (removes dist/ first)
npm run start          # Run compiled server
npm run start:stdio    # Run server with STDIO transport
npm run start:http     # Run server with HTTP transport
npm run start:agent    # Run autonomous agent CLI

# Development Tools
npm run lint           # Run ESLint
npm run lint:fix       # Fix ESLint issues automatically
npm run format         # Format code with Prettier
npm run docs:generate  # Generate TypeDoc documentation
npm run tree           # Generate project structure tree

# Utilities
npm run inspector      # Run MCP inspector for testing
npm run jwt-token      # Generate JWT tokens for testing
npm run fetch-spec     # Fetch OpenAPI specifications
npm run db:duckdb-example  # Run DuckDB example
```

### Build Configuration

- **tsconfig.json** - TypeScript compiler configuration (ES2020, ESM)
- **eslint.config.js** - ESLint rules with TypeScript support
- **typedoc.json** - API documentation generation
- **tsdoc.json** - TSDoc configuration for documentation

### Module System

- **ESM modules** - Uses ES6 import/export syntax
- **File extensions** - All imports must include `.js` extension (compiled output)
- **ts-node/esm** - ESM loader for TypeScript execution

### Environment Configuration

- **dotenv** - Environment variable loading from `.env`
- **Zod schemas** - Runtime validation of environment variables
- **Multi-environment** - Development/production configurations