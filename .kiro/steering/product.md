# Product Overview

**MCP TypeScript Template** is a production-ready template for building Model Context Protocol (MCP) servers, clients, and autonomous agents. It provides a comprehensive three-part architecture:

## Core Components

1. **🤖 Agent Framework** (`src/agent/`) - Autonomous agents that connect to multiple MCP servers, discover tools, and execute complex tasks based on user prompts
2. **🔌 MCP Server** (`src/mcp-server/`) - Extensible server hosting custom tools and resources, supporting both STDIO and HTTP transports
3. **💻 MCP Client** (`src/mcp-client/`) - Robust client for connecting to MCP-compliant servers with configuration-driven setup

## Key Features

- **Multi-transport Support**: STDIO and Streamable HTTP with authentication (JWT/OAuth 2.1)
- **Production Utilities**: Comprehensive logging, error handling, security, rate limiting
- **Type Safety**: Full TypeScript with Zod validation throughout
- **Service Integrations**: OpenRouter (LLM), OpenWeatherMap, DuckDB, Supabase
- **Built-in Tools**: Echo, Weather, Cat Facts, Image Test tools as examples
- **Agent Framework**: Autonomous task execution with tool discovery and multi-server connections

## Target Use Cases

- Building MCP servers for specific domains (clinical trials, PubMed, Git, Obsidian, etc.)
- Creating autonomous agents that leverage multiple MCP servers
- Developing production-ready MCP applications with proper error handling and logging
- Rapid prototyping of MCP-based integrations