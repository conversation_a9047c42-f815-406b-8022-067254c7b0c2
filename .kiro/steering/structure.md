# Project Structure & Organization

## Directory Layout

```
src/
├── agent/                    # 🤖 Autonomous Agent Framework
│   ├── agent-core/          # Core agent logic and lifecycle
│   └── cli/                 # Command-line interface for agents
├── mcp-server/              # 🔌 MCP Server Implementation
│   ├── tools/               # Custom MCP tools (echo, weather, etc.)
│   ├── resources/           # MCP resources (echo resource)
│   └── transports/          # Communication protocols (stdio, http, auth)
├── mcp-client/              # 💻 MCP Client Implementation
│   ├── core/                # Client connection and management logic
│   ├── client-config/       # Configuration loading and validation
│   └── transports/          # Client transport implementations
├── config/                  # ⚙️ Application Configuration
├── services/                # 🔗 External Service Integrations
│   ├── llm-providers/       # LLM service providers (OpenRouter)
│   ├── duck-db/             # DuckDB database service
│   └── supabase/            # Supabase backend service
├── utils/                   # 🛠️ Utility Functions
│   ├── internal/            # Core utilities (logging, error handling)
│   ├── security/            # Security utilities (sanitization, rate limiting)
│   ├── parsing/             # Data parsing utilities
│   ├── network/             # Network utilities
│   ├── metrics/             # Metrics and monitoring
│   └── scheduling/          # Task scheduling utilities
├── types-global/            # 📝 Global Type Definitions
└── storage/                 # 💾 Data Storage Examples
```

## Architectural Patterns

### Tool/Resource Structure
Each MCP tool/resource follows a consistent pattern:
```
toolName/
├── index.ts          # Main export and public interface
├── logic.ts          # Core business logic implementation
└── registration.ts   # MCP server registration logic
```

### Service Layer Pattern
External services are abstracted into dedicated service classes:
- **Provider Pattern**: LLM providers implement common interface
- **Connection Management**: Database connections handled centrally
- **Configuration Driven**: Services configured via environment variables

### Transport Layer
Both client and server support multiple transports:
- **STDIO Transport**: Process-based communication
- **HTTP Transport**: RESTful API with Server-Sent Events
- **Authentication**: Pluggable auth strategies (JWT, OAuth 2.1)

### Error Handling Strategy
- **Centralized Error Handler**: `src/utils/internal/errorHandler.ts`
- **Typed Errors**: Custom error types with error codes
- **Context Propagation**: Request context tracked throughout call chain
- **Graceful Degradation**: Non-critical failures don't crash the system

### Configuration Management
- **Environment-based**: All config from environment variables
- **Zod Validation**: Runtime validation of configuration
- **Hierarchical**: Package.json → Environment → Defaults
- **Type-safe**: Full TypeScript types for all configuration

## File Naming Conventions

### TypeScript Files
- **camelCase** for file names: `clientManager.ts`, `errorHandler.ts`
- **PascalCase** for classes: `Agent`, `McpClientManager`
- **kebab-case** for directories: `mcp-server`, `client-config`

### Import/Export Patterns
- **Barrel exports**: `index.ts` files re-export module contents
- **Explicit imports**: Always specify `.js` extension for compiled output
- **Named exports**: Prefer named exports over default exports

### Documentation
- **JSDoc comments**: Comprehensive documentation for all public APIs
- **File headers**: `@fileoverview` describing module purpose
- **Type annotations**: Explicit types even when TypeScript can infer

## Key Architectural Principles

### Separation of Concerns
- **Agent**: High-level task orchestration
- **Client**: MCP protocol communication
- **Server**: Tool/resource hosting
- **Services**: External system integration
- **Utils**: Cross-cutting concerns

### Dependency Injection
- **Configuration**: Injected via environment variables
- **Services**: Passed as constructor parameters
- **Context**: Request context propagated through call chain

### Error Boundaries
- **Try/Catch Wrappers**: Consistent error handling patterns
- **Graceful Shutdown**: Proper cleanup on process termination
- **Circuit Breakers**: Fail-fast for external service failures

### Logging Strategy
- **Structured Logging**: JSON-formatted log entries
- **Context Correlation**: Request IDs track operations
- **Multiple Transports**: Console, file, and custom transports
- **Log Levels**: Debug, info, warning, error with filtering