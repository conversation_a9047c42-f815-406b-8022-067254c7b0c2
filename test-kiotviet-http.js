#!/usr/bin/env node

/**
 * Test script for KiotViet Categories MCP tool via HTTP transport
 * This script demonstrates the proper way to test MCP tools over HTTP
 * 
 * Usage: node test-kiotviet-http.js
 */

const MCP_SERVER_URL = 'http://localhost:3010/mcp';
const SESSION_ID = `test-session-${Date.now()}`;

console.log('🧪 Testing KiotViet Categories MCP Tool via HTTP');
console.log('================================================');
console.log(`Server URL: ${MCP_SERVER_URL}`);
console.log(`Session ID: ${SESSION_ID}`);
console.log('');

/**
 * Make an MCP request to the server
 * @param {string} method - The MCP method to call
 * @param {object} params - The parameters for the method
 * @param {number} id - The request ID
 * @returns {Promise<object>} The response from the server
 */
async function makeMcpRequest(method, params, id) {
  const requestBody = {
    jsonrpc: '2.0',
    id: id,
    method: method,
    params: params
  };
  
  console.log(`📤 Request (${method}):`);
  console.log(JSON.stringify(requestBody, null, 2));
  console.log('');
  
  try {
    const response = await fetch(MCP_SERVER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/event-stream',
        'Mcp-Session-Id': SESSION_ID
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const responseData = await response.json();
    
    console.log('📥 Response:');
    console.log(JSON.stringify(responseData, null, 2));
    console.log('');
    
    if (responseData.error) {
      console.log('❌ Error detected in response');
      return { success: false, data: responseData };
    }
    
    return { success: true, data: responseData };
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Main test function
 */
async function runTests() {
  try {
    // Step 1: Initialize MCP session
    console.log('🔄 Step 1: Initializing MCP session...');
    const initResult = await makeMcpRequest('initialize', {
      protocolVersion: '2025-03-26',
      capabilities: {},
      clientInfo: {
        name: 'test-client',
        version: '1.0.0'
      }
    }, 1);
    
    if (!initResult.success) {
      console.log('❌ Failed to initialize session');
      process.exit(1);
    }
    
    console.log('✅ Session initialized successfully');
    console.log('');
    
    // Step 2: List available tools
    console.log('🔄 Step 2: Listing available tools...');
    const toolsResult = await makeMcpRequest('tools/list', {}, 2);
    
    if (!toolsResult.success) {
      console.log('❌ Failed to list tools');
      process.exit(1);
    }
    
    console.log('✅ Tools listed successfully');
    console.log('');
    
    // Step 3: Call KiotViet Categories tool
    console.log('🔄 Step 3: Calling KiotViet Categories tool...');
    const categoriesResult = await makeMcpRequest('tools/call', {
      name: 'kiotviet_categories',
      arguments: {
        action: 'list'
      }
    }, 3);
    
    if (!categoriesResult.success) {
      console.log('❌ Failed to call KiotViet Categories tool');
      console.log('Error details:', categoriesResult.error || categoriesResult.data?.error);
    } else {
      console.log('✅ KiotViet Categories tool called successfully');
    }
    console.log('');
    
    // Step 4: Test error handling with invalid action
    console.log('🔄 Step 4: Testing error handling with invalid action...');
    const errorResult = await makeMcpRequest('tools/call', {
      name: 'kiotviet_categories',
      arguments: {
        action: 'invalid_action'
      }
    }, 4);
    
    console.log('✅ Error handling test completed (expected to show error)');
    console.log('');
    
    console.log('🎉 All tests completed!');
    console.log('');
    console.log('📝 Notes:');
    console.log('- Make sure you have Node.js >=20.0.0 installed');
    console.log('- Ensure KiotViet environment variables are set:');
    console.log('  - KIOTVIET_CLIENT_ID');
    console.log('  - KIOTVIET_CLIENT_SECRET');
    console.log('  - KIOTVIET_RETAILER');
    console.log('- Start the server with: npm run start:http');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.error('❌ This script requires Node.js 18+ with built-in fetch support');
  console.error('Current Node.js version:', process.version);
  console.error('Please upgrade to Node.js 20+ as required by the project');
  process.exit(1);
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
