/**
 * @fileoverview Type definitions for KiotViet API
 */

export interface KiotVietConfig {
  clientId: string;
  clientSecret: string;
  retailer: string;
  baseUrl: string;
  tokenUrl: string;
}

export interface KiotVietAuthCredentials {
  clientId: string;
  clientSecret: string;
  retailer: string;
}

export interface KiotVietToken {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
  obtained_at: number; // timestamp when token was obtained
}

export interface KiotVietCategory {
  categoryId: number;
  categoryName: string;
  categoryCode: string;
  parentId?: number;
  hasChild: boolean;
  createdDate: string;
  modifiedDate: string;
}

export interface KiotVietApiResponse<T> {
  data: T;
  pageSize?: number;
  pageIndex?: number;
  total?: number;
}

export interface KiotVietErrorResponse {
  error: string;
  error_description: string;
}