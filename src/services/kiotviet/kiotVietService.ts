/**
 * @fileoverview KiotViet API service implementation
 */

import { logger } from '../../utils/internal/logger.js';
import { KIOTVIET_GRANT_TYPE, KIOTVIET_SCOPE } from './constants.js';

import type { 
  KiotVietConfig, 
  KiotVietToken, 
  KiotVietCategory, 
  KiotVietApiResponse,
  KiotVietErrorResponse 
} from './types.js';

export class KiotVietService {
  private token: KiotVietToken | null = null;
  private readonly TOKEN_BUFFER_TIME = 300; // 5 minutes buffer before expiry

  // KIOTVIET APIs
  private GET_CATEGORIES_URL: string = '/categories';
  private SOMETHING_API_URL: string = '/some-api-url';

  constructor(private readonly config: KiotVietConfig) {}

  private async getValidToken(): Promise<string> {
    if (this.isTokenValid()) {
      return this.token!.access_token;
    }

    await this.getNewToken();
    return this.token!.access_token;
  }

  private isTokenValid(): boolean {
    if (!this.token) return false;
    
    const now = Math.floor(Date.now() / 1000);
    const expiresAt = this.token.obtained_at + this.token.expires_in - this.TOKEN_BUFFER_TIME;
    
    return now < expiresAt;
  }

  private async getNewToken(): Promise<void> {
    try {
      const response = await fetch(this.config.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: KIOTVIET_GRANT_TYPE,
          client_id: this.config.clientId,
          client_secret: this.config.clientSecret,
          scope: KIOTVIET_SCOPE,
        }),
      });

      if (!response.ok) {
        const errorData: KiotVietErrorResponse = await response.json();
        throw new Error(`Token request failed: ${errorData.error_description || errorData.error}`);
      }

      const tokenData = await response.json();
      this.token = {
        ...tokenData,
        obtained_at: Math.floor(Date.now() / 1000),
      };
    } catch (error) {
      logger.error('Failed to refresh KiotViet token:', error as Error);
      throw error;
    }
  }

  /**
   * HÀM COMMON CALL API KIOTVIET.
   * 
   * @param endpoint
   * @param options 
   * @returns 
   */
  private async callAPI<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const token = await this.getValidToken();
    
    const response = await fetch(`${this.config.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Retailer': this.config.retailer,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`[callAPI] KiotViet API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * API Lấy danh sách nhóm hàng.
   * 
   * @returns KiotVietCategory
   */
  async getProductCategories(): Promise<KiotVietCategory[]> {
    try {
      logger.info('[KIOTVIET][getProductCategories] Fetching product categories from KiotViet');
      const response = await this.callAPI<KiotVietApiResponse<KiotVietCategory[]>>(this.GET_CATEGORIES_URL);
      return response.data;
    } catch (error) {
      logger.error('[KIOTVIET][getProductCategories] Failed to fetch product categories:', error as Error);
      throw error;
    }
  }
}