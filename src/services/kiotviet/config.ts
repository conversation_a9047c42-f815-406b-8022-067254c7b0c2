/**
 * @fileoverview KiotViet API configuration
 */

import { z } from 'zod';

const KiotVietConfigSchema = z.object({
    clientId: z.string().min(1, 'KiotViet Client ID is required'),
    clientSecret: z.string().min(1, 'KiotViet Client Secret is required'),
    retailer: z.string().min(1, 'KiotViet Retailer is required'),
    baseUrl: z.string().url().default('https://public.kiotapi.com'),
    tokenUrl: z.string().url().default('https://id.kiotviet.vn/connect/token'),
});

export type KiotVietConfig = z.infer<typeof KiotVietConfigSchema>;

export function createKiotVietConfig(): KiotVietConfig {
    return KiotVietConfigSchema.parse({
        clientId: process.env.KIOTVIET_CLIENT_ID,
        clientSecret: process.env.KIOTVIET_CLIENT_SECRET,
        retailer: process.env.KIOTVIET_RETAILER,
        baseUrl: process.env.KIOTVIET_BASE_URL,
        tokenUrl: process.env.KIOTVIET_TOKEN_URL,
    });
}