/**
 * @fileoverview Defines the core logic, schemas, and types for the `get_weather` tool.
 * This tool fetches current weather information for a specified city using OpenWeatherMap API.
 * @module src/mcp-server/tools/weatherTool/logic
 */

import { z } from "zod";
import { BaseErrorCode, McpError } from "../../../types-global/errors.js";
import {
  fetchWithTimeout,
  logger,
  type RequestContext,
} from "../../../utils/index.js";
import { config } from "../../../config/index.js";

/**
 * Zod schema for validating input arguments for the `get_weather` tool.
 */
export const WeatherToolInputSchema = z
  .object({
    city: z
      .string()
      .min(1, "City name cannot be empty.")
      .max(100, "City name must be less than 100 characters.")
      .describe("The name of the city to get weather information for."),
    country: z
      .string()
      .length(2, "Country code must be exactly 2 characters (ISO 3166).")
      .optional()
      .describe("Optional: Two-letter country code (e.g., 'US', 'VN', 'JP')."),
    units: z
      .enum(["metric", "imperial", "kelvin"])
      .default("metric")
      .describe("Temperature units: 'metric' (Celsius), 'imperial' (Fahrenheit), or 'kelvin'."),
  })
  .describe(
    "Input schema for the get_weather tool. Requires a city name and optionally country code and units.",
  );

/**
 * TypeScript type inferred from `WeatherToolInputSchema`.
 */
export type WeatherToolInput = z.infer<typeof WeatherToolInputSchema>;

/**
 * Defines the structure of the JSON payload returned by the `get_weather` tool handler.
 */
export interface WeatherToolResponse {
  city: string;
  country: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  weather: {
    main: string;
    description: string;
    icon: string;
  };
  temperature: {
    current: number;
    feelsLike: number;
    min: number;
    max: number;
    units: string;
  };
  humidity: number;
  pressure: number;
  windSpeed: number;
  visibility?: number;
  cloudiness: number;
  sunrise: string;
  sunset: string;
  timezone: string;
  timestamp: string;
}

/**
 * Interface for OpenWeatherMap API response
 */
interface OpenWeatherMapResponse {
  coord: { lon: number; lat: number };
  weather: Array<{
    id: number;
    main: string;
    description: string;
    icon: string;
  }>;
  base: string;
  main: {
    temp: number;
    feels_like: number;
    temp_min: number;
    temp_max: number;
    pressure: number;
    humidity: number;
  };
  visibility?: number;
  wind: {
    speed: number;
    deg?: number;
  };
  clouds: {
    all: number;
  };
  dt: number;
  sys: {
    type?: number;
    id?: number;
    country: string;
    sunrise: number;
    sunset: number;
  };
  timezone: number;
  id: number;
  name: string;
  cod: number;
}

/**
 * Gets the OpenWeatherMap API key from environment variables
 */
function getApiKey(): string {
  const apiKey = process.env.OPENWEATHER_API_KEY;
  if (!apiKey) {
    throw new McpError(
      BaseErrorCode.CONFIGURATION_ERROR,
      "OpenWeatherMap API key is not configured. Please set OPENWEATHER_API_KEY environment variable."
    );
  }
  return apiKey;
}

/**
 * Formats Unix timestamp to readable time string
 */
function formatUnixTime(unixTimestamp: number, timezoneOffset: number): string {
  const date = new Date((unixTimestamp + timezoneOffset) * 1000);
  return date.toISOString();
}

/**
 * Gets the temperature unit string for display
 */
function getTemperatureUnitString(units: string): string {
  switch (units) {
    case "metric": return "°C";
    case "imperial": return "°F";
    case "kelvin": return "K";
    default: return "°C";
  }
}

/**
 * Processes the core logic for the `get_weather` tool.
 * It calls the OpenWeatherMap API and returns the weather data.
 * @param params - The validated input parameters for the tool.
 * @param context - The request context for logging and tracing.
 * @returns A promise that resolves to an object containing the weather data.
 * @throws {McpError} If the API request fails or returns an error.
 */
export async function weatherToolLogic(
  params: WeatherToolInput,
  context: RequestContext,
): Promise<WeatherToolResponse> {
  logger.debug("Processing get_weather logic.", {
    ...context,
    input: params,
    toolInput: params,
  });

  const apiKey = getApiKey();
  
  // Build the city query string
  let cityQuery = params.city;
  if (params.country) {
    cityQuery += `,${params.country}`;
  }

  const apiUrl = new URL("https://api.openweathermap.org/data/2.5/weather");
  apiUrl.searchParams.set("q", cityQuery);
  apiUrl.searchParams.set("appid", apiKey);
  apiUrl.searchParams.set("units", params.units);

  logger.info(`Fetching weather data from: ${apiUrl.toString().replace(apiKey, 'HIDDEN_API_KEY')}`, context);

  const WEATHER_API_TIMEOUT_MS = 10000;

  const response = await fetchWithTimeout(
    apiUrl.toString(),
    WEATHER_API_TIMEOUT_MS,
    context,
  );

  if (!response.ok) {
    const errorText = await response.text();
    let errorMessage = `OpenWeatherMap API request failed: ${response.status} ${response.statusText}`;
    
    // Provide more specific error messages for common issues
    if (response.status === 401) {
      errorMessage = "Invalid API key. Please check your OPENWEATHER_API_KEY environment variable.";
    } else if (response.status === 404) {
      errorMessage = `City "${params.city}" not found. Please check the city name and country code.`;
    } else if (response.status === 429) {
      errorMessage = "API rate limit exceeded. Please try again later.";
    }

    throw new McpError(
      BaseErrorCode.SERVICE_UNAVAILABLE,
      errorMessage,
      {
        ...context,
        httpStatusCode: response.status,
        responseBody: errorText,
        cityQuery,
      },
    );
  }

  const data: OpenWeatherMapResponse = await response.json();

  // Validate response structure
  if (!data.main || !data.weather || data.weather.length === 0) {
    throw new McpError(
      BaseErrorCode.VALIDATION_ERROR,
      "Invalid response structure from OpenWeatherMap API",
      { ...context, responseData: data }
    );
  }

  const weatherInfo = data.weather[0];
  const unitString = getTemperatureUnitString(params.units);

  const toolResponse: WeatherToolResponse = {
    city: data.name,
    country: data.sys.country,
    coordinates: {
      latitude: data.coord.lat,
      longitude: data.coord.lon,
    },
    weather: {
      main: weatherInfo.main,
      description: weatherInfo.description,
      icon: weatherInfo.icon,
    },
    temperature: {
      current: Math.round(data.main.temp * 10) / 10,
      feelsLike: Math.round(data.main.feels_like * 10) / 10,
      min: Math.round(data.main.temp_min * 10) / 10,
      max: Math.round(data.main.temp_max * 10) / 10,
      units: unitString,
    },
    humidity: data.main.humidity,
    pressure: data.main.pressure,
    windSpeed: data.wind.speed,
    visibility: data.visibility,
    cloudiness: data.clouds.all,
    sunrise: formatUnixTime(data.sys.sunrise, data.timezone),
    sunset: formatUnixTime(data.sys.sunset, data.timezone),
    timezone: `UTC${data.timezone >= 0 ? '+' : ''}${data.timezone / 3600}`,
    timestamp: new Date().toISOString(),
  };

  logger.info("Weather data fetched and processed successfully.", {
    ...context,
    city: toolResponse.city,
    country: toolResponse.country,
    temperature: toolResponse.temperature.current,
    weather: toolResponse.weather.main,
  });

  return toolResponse;
} 