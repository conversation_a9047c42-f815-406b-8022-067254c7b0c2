/**
 * @fileoverview Handles the registration of the `get_weather` tool
 * with an MCP server instance. This tool fetches current weather information
 * for a specified city using the OpenWeatherMap API.
 * @module src/mcp-server/tools/weatherTool/registration
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import type { CallToolResult } from "@modelcontextprotocol/sdk/types.js";
import { BaseErrorCode, McpError } from "../../../types-global/errors.js";
import {
  ErrorHandler,
  logger,
  RequestContext,
  requestContextService,
} from "../../../utils/index.js";
import {
  WeatherToolInput,
  WeatherToolInputSchema,
  weatherToolLogic,
} from "./logic.js";

/**
 * Registers the 'get_weather' tool and its handler with the MCP server.
 *
 * @param server - The MCP server instance to register the tool with.
 * @returns A promise that resolves when tool registration is complete.
 */
export const registerWeatherTool = async (
  server: McpServer,
): Promise<void> => {
  const toolName = "get_weather";
  const toolDescription = `Get current weather information for a specified city using OpenWeatherMap API.

Features:
- Current weather conditions (temperature, description, humidity, etc.)
- Sunrise/sunset times
- Wind speed and pressure information
- Supports multiple temperature units (Celsius, Fahrenheit, Kelvin)
- Optional country code for more precise location matching

Required Environment Variable:
- OPENWEATHER_API_KEY: Your OpenWeatherMap API key (free at openweathermap.org)

Examples:
- {"city": "Ho Chi Minh City", "country": "VN", "units": "metric"}
- {"city": "New York", "country": "US", "units": "imperial"}
- {"city": "Tokyo", "units": "metric"}`;

  const registrationContext: RequestContext =
    requestContextService.createRequestContext({
      operation: "RegisterTool",
      toolName: toolName,
    });

  logger.info(`Registering tool: '${toolName}'`, registrationContext);

  await ErrorHandler.tryCatch(
    async () => {
      server.tool(
        toolName,
        toolDescription,
        WeatherToolInputSchema.shape,
        async (
          params: WeatherToolInput,
          mcpContext: unknown,
        ): Promise<CallToolResult> => {
          const handlerContext: RequestContext =
            requestContextService.createRequestContext({
              parentRequestId: registrationContext.requestId,
              operation: "HandleToolRequest",
              toolName: toolName,
              mcpToolContext: mcpContext,
              input: params,
            });

          try {
            const result = await weatherToolLogic(params, handlerContext);
            
            // Format the response in a user-friendly way
            const formattedResponse = {
              summary: `Current weather in ${result.city}, ${result.country}`,
              weather: {
                condition: result.weather.description,
                temperature: `${result.temperature.current}${result.temperature.units}`,
                feelsLike: `${result.temperature.feelsLike}${result.temperature.units}`,
                humidity: `${result.humidity}%`,
                windSpeed: `${result.windSpeed} m/s`,
                pressure: `${result.pressure} hPa`,
                cloudiness: `${result.cloudiness}%`,
              },
              location: {
                coordinates: `${result.coordinates.latitude}°N, ${result.coordinates.longitude}°E`,
                timezone: result.timezone,
              },
              sun: {
                sunrise: result.sunrise,
                sunset: result.sunset,
              },
              details: result,
              timestamp: result.timestamp,
            };

            return {
              content: [
                { 
                  type: "text", 
                  text: JSON.stringify(formattedResponse, null, 2) 
                },
              ],
              isError: false,
            };
          } catch (error) {
            const handledError = ErrorHandler.handleError(error, {
              operation: "weatherToolHandler",
              context: handlerContext,
              input: params,
            });

            const mcpError =
              handledError instanceof McpError
                ? handledError
                : new McpError(
                    BaseErrorCode.INTERNAL_ERROR,
                    "An unexpected error occurred while fetching weather data.",
                    { originalErrorName: handledError.name },
                  );

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify({
                    error: {
                      code: mcpError.code,
                      message: mcpError.message,
                      details: mcpError.details,
                    },
                  }),
                },
              ],
              isError: true,
            };
          }
        },
      );

      logger.info(
        `Tool '${toolName}' registered successfully.`,
        registrationContext,
      );
    },
    {
      operation: `RegisteringTool_${toolName}`,
      context: registrationContext,
      errorCode: BaseErrorCode.INITIALIZATION_FAILED,
      critical: true,
    },
  );
}; 