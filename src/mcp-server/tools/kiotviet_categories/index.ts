/**
 * @fileoverview Barrel file for the KiotViet Categories tool.
 * This file serves as the public interface for the KiotViet Categories tool module,
 * primarily exporting the `registerKiotVietCategoriesTool` function. This function is
 * responsible for registering the KiotViet Categories tool with an MCP server instance,
 * making it available for invocation by clients.
 *
 * Consuming modules should import from this barrel file to access
 * the KiotViet Categories tool's registration capabilities.
 */

export { registerKiotVietCategoriesTool } from './registerTool.js';