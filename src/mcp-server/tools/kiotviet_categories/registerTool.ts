/**
 * @fileoverview Handles the registration of the KiotViet Categories tool with an MCP server instance.
 * This module defines the tool's metadata, its input schema shape,
 * and the asynchronous handler function that processes tool invocation requests.
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import type { CallToolResult } from "@modelcontextprotocol/sdk/types.js";
import { BaseErrorCode, McpError } from "../../../types-global/errors.js";
import {
  ErrorHandler,
  logger,
  RequestContext,
  requestContextService,
} from "../../../utils/index.js";

import { 
  KiotVietCategoriesInput, 
  KiotVietCategoriesInputSchema, 
  kiotVietCategoriesLogic 
} from "./logic.js";

/**
 * Registers the 'kiotviet_categories' tool and its handler with the provided MCP server instance.
 *
 * @param server - The MCP server instance to register the tool with.
 * @returns A promise that resolves when the tool registration is complete.
 */
export const registerKiotVietCategoriesTool = async (server: McpServer): Promise<void> => {
  const toolName = "kiotviet_categories";
  const toolDescription = "Access KiotViet product categories. Supports listing all categories, getting specific category by ID, and testing API connection.";

  const registrationContext: RequestContext =
    requestContextService.createRequestContext({
      operation: "RegisterTool",
      toolName: toolName,
    });

  await ErrorHandler.tryCatch(
    async () => {
      server.tool(
        toolName,
        toolDescription,
        KiotVietCategoriesInputSchema.shape,
        async (
          params: KiotVietCategoriesInput,
          mcpContext: unknown,
        ): Promise<CallToolResult> => {
          const validatedParams = KiotVietCategoriesInputSchema.parse(params);
          
          const handlerContext: RequestContext =
            requestContextService.createRequestContext({
              parentRequestId: registrationContext.requestId,
              operation: "HandleToolRequest",
              toolName: toolName,
              mcpToolContext: mcpContext,
              input: validatedParams,
            });

          try {
            const result = await kiotVietCategoriesLogic(validatedParams, handlerContext);

            logger.debug("KiotViet Categories tool completed successfully", {
              ...handlerContext,
              responseSize: JSON.stringify(result).length,
            });

            return {
              content: [
                { type: "text", text: JSON.stringify(result, null, 2) },
              ],
              isError: false,
            };
          } catch (error) {
            const handledError = ErrorHandler.handleError(error, {
              operation: "kiotVietCategoriesHandler",
              context: handlerContext,
              input: validatedParams,
            });

            const mcpError =
              handledError instanceof McpError
                ? handledError
                : new McpError(
                    BaseErrorCode.INTERNAL_ERROR,
                    "An unexpected error occurred in the KiotViet Categories tool.",
                    { originalErrorName: handledError.name },
                  );

            logger.error("KiotViet Categories tool failed", {
              ...handlerContext,
              error: mcpError.message,
              errorCode: mcpError.code,
            });

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify({
                    error: {
                      code: mcpError.code,
                      message: mcpError.message,
                      details: mcpError.details,
                    },
                  }),
                },
              ],
              isError: true,
            };
          }
        },
      );
    },
    {
      operation: `RegisteringTool_${toolName}`,
      context: registrationContext,
      errorCode: BaseErrorCode.INITIALIZATION_FAILED,
      critical: true,
    },
  );
};