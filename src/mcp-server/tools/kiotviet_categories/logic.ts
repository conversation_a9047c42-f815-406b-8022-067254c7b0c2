/**
 * @fileoverview KiotViet Categories tool implementation
 */

import { z } from 'zod';
import { KiotVietService, createKiotVietConfig } from '../../../services/kiotviet/index.js';
import { logger, RequestContext } from '../../../utils/index.js';

export const KiotVietCategoriesInputSchema = z.object({
  action: z.enum(['list']).describe('Action to perform: list (get all categories)'),
});

export type KiotVietCategoriesInput = z.infer<typeof KiotVietCategoriesInputSchema>;

let kiotVietService: KiotVietService | null = null;

function getKiotVietService(): KiotVietService {
  if (!kiotVietService) {
    try {
      const config = createKiotVietConfig();
      kiotVietService = new KiotVietService(config);
    } catch (error) {
      throw new Error('KiotViet service configuration error. Please check environment variables.');
    }
  }
  return kiotVietService;
}

/**
 * Core logic for KiotViet Categories tool
 * @param input
 * @param context 
 * @returns 
 */
export async function kiotVietCategoriesLogic(
  input: KiotVietCategoriesInput,
  context: RequestContext
): Promise<any> {
  logger.info(`KiotViet Categories tool called with action: ${input.action}`, context);

  const service = getKiotVietService();
  const categories = await service.getProductCategories();

  return {
    success: true,
    data: categories,
    count: categories.length,
    message: `Retrieved ${categories.length} product categories from KiotViet`,
  };
}