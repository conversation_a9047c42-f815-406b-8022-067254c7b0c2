#!/bin/bash

# Test script for KiotViet Categories MCP tool via HTTP transport
# This script demonstrates the proper way to test MCP tools over HTTP

set -e

# Configuration
MCP_SERVER_URL="http://localhost:3010/mcp"
SESSION_ID="test-session-$(date +%s)"

echo "🧪 Testing KiotViet Categories MCP Tool via HTTP"
echo "================================================"
echo "Server URL: $MCP_SERVER_URL"
echo "Session ID: $SESSION_ID"
echo ""

# Function to make MCP requests
make_mcp_request() {
    local method="$1"
    local params="$2"
    local id="$3"
    
    local request_body=$(cat <<EOF
{
  "jsonrpc": "2.0",
  "id": $id,
  "method": "$method",
  "params": $params
}
EOF
)
    
    echo "📤 Request ($method):"
    echo "$request_body" | jq .
    echo ""
    
    local response=$(curl -s -X POST "$MCP_SERVER_URL" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json, text/event-stream" \
        -H "Mcp-Session-Id: $SESSION_ID" \
        -d "$request_body")
    
    echo "📥 Response:"
    echo "$response" | jq .
    echo ""
    
    # Check for errors
    if echo "$response" | jq -e '.error' > /dev/null; then
        echo "❌ Error detected in response"
        return 1
    fi
    
    return 0
}

# Step 1: Initialize MCP session
echo "🔄 Step 1: Initializing MCP session..."
make_mcp_request "initialize" '{
  "protocolVersion": "2025-03-26",
  "capabilities": {},
  "clientInfo": {
    "name": "test-client",
    "version": "1.0.0"
  }
}' 1

if [ $? -ne 0 ]; then
    echo "❌ Failed to initialize session"
    exit 1
fi

echo "✅ Session initialized successfully"
echo ""

# Step 2: List available tools
echo "🔄 Step 2: Listing available tools..."
make_mcp_request "tools/list" '{}' 2

if [ $? -ne 0 ]; then
    echo "❌ Failed to list tools"
    exit 1
fi

echo "✅ Tools listed successfully"
echo ""

# Step 3: Call KiotViet Categories tool
echo "🔄 Step 3: Calling KiotViet Categories tool..."
make_mcp_request "tools/call" '{
  "name": "kiotviet_categories",
  "arguments": {
    "action": "list"
  }
}' 3

if [ $? -ne 0 ]; then
    echo "❌ Failed to call KiotViet Categories tool"
    exit 1
fi

echo "✅ KiotViet Categories tool called successfully"
echo ""

# Step 4: Test error handling with invalid action
echo "🔄 Step 4: Testing error handling with invalid action..."
make_mcp_request "tools/call" '{
  "name": "kiotviet_categories",
  "arguments": {
    "action": "invalid_action"
  }
}' 4

# This should fail, but we want to see the error response
echo "✅ Error handling test completed (expected to show error)"
echo ""

echo "🎉 All tests completed!"
echo ""
echo "📝 Notes:"
echo "- Make sure you have Node.js >=20.0.0 installed"
echo "- Ensure KiotViet environment variables are set:"
echo "  - KIOTVIET_CLIENT_ID"
echo "  - KIOTVIET_CLIENT_SECRET"
echo "  - KIOTVIET_RETAILER"
echo "- Start the server with: npm run start:http"
echo "- Install jq for JSON formatting: brew install jq (macOS) or apt-get install jq (Ubuntu)"
